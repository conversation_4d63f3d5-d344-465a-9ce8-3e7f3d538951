import { use<PERSON><PERSON>, useAtomValue, useSet<PERSON><PERSON> } from 'jotai'
import {
  resourceTemplate<PERSON>tom,
  screenOrientation<PERSON>tom,
  isUseCrowd<PERSON>tom,
  isTaskTypeSelectedAtom,
  isSupportVideoAtom,
  isShowThemeDetailModalAtom,
  selectedThemeD<PERSON>il<PERSON>tom,
  selectedGender<PERSON><PERSON>,
  selectedEventDetail<PERSON>tom,
} from '@/stores'
import SexTab from '@/components/ui/SexTab'
// import { AutoScroll } from '@/components/ui/AutoScroll'
import { useEffect, useMemo, useState } from 'react'
import { MirrorSexEnum, PopulationCategory } from '@/graphqls/types'
// import classnames from 'classnames'
import { ActiveTemplateItem } from '@/components/pages/homepage/const'
import SingleTemplateList from '@/components/pages/homepage/SingleTemplateList'
import MazeSingleTemplateList from '@/components/pages/homepage/MazeSingleTemplateList'
import MultipleTemplateList from '@/components/pages/homepage/MultipleTemplateList'
import classNames from 'classnames'
import { CreateBtn } from './CreateBtn'
import BackToHome from '@/components/business/BackToHome'
import { useTranslation } from 'react-i18next'
import { ThemeDetailModal } from './ThemeDetailModal'
import { ThemeDetail } from '@/apis/types'
import { isMachine, isPhone } from '@/utils'
import useSWR from 'swr'
import _api from '@/apis/maze.api'
import _ajax from '@/utils/ajax'

export const PictureFramework = () => {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [, setIsTaskTypeSelected] = useAtom(isTaskTypeSelectedAtom)
  const [isSupportVideo] = useAtom(isSupportVideoAtom)
  const { t } = useTranslation()

  const genders = [
    {
      label: '女性',
      value: MirrorSexEnum.FEMALE,
    },
    {
      label: '男性',
      value: MirrorSexEnum.MALE,
    },
  ]

  // 选中的模板
  const [activeTemplate, setActiveTemplate] = useState<
    ThemeDetail | undefined | null
  >()
  // 选中的性别
  const [activeGender, setActiveGender] = useAtom(selectedGenderAtom)
  // 选中的分类id
  const [activeTab, setActiveTab] = useState<number | undefined>(undefined)

  const [resourceTemplate] = useAtom(resourceTemplateAtom)

  const [themeDetailModalOpen, setThemeDetailModalOpen] = useAtom(
    isShowThemeDetailModalAtom
  )

  const setSelectedThemeDetail = useSetAtom(selectedThemeDetailAtom)
  const [selectedEventDetail] = useAtom(selectedEventDetailAtom)

  useEffect(() => {
    if (activeTemplate) {
      setSelectedThemeDetail(activeTemplate)
    }
  }, [activeTemplate])

  // 使用所有分类下的模版并去重
  const selectTemplateList = useMemo(() => {
    const selectedThemes = selectedEventDetail?.event_themes || []
    return selectedThemes
  }, [selectedEventDetail])

  // console.log(selectTemplateList, resourceTemplate)

  // 设置默认选中模板
  useEffect(() => {
    const middleIndex = screenOrientation.isLandScape
      ? 0
      : Math.floor((selectTemplateList.length - 1) / 2)
    setActiveTemplate(selectTemplateList?.[middleIndex])
  }, [selectTemplateList])

  return (
    <>
      {isSupportVideo && (
        <BackToHome
          onCustomBack={() => {
            setIsTaskTypeSelected(false)
          }}
        />
      )}
      <div className="relative w-full h-full">
        <div className="w-[80%] absolute top-2 z-10 left-[50%] translate-x-[-50%]">
          <h1 className="maze-page-title">{t('选择您喜欢的风格')}</h1>
          <div
            className={classNames(
              'relative z-10 w-auto mt-8 ipad:mt-6',
              screenOrientation.isPortrait
                ? ' gap-6'
                : ' inline-block left-[50%] translate-x-[-50%]'
            )}
          >
            <SexTab
              items={genders}
              activeKey={activeGender}
              setActiveKey={val => setActiveGender(val)}
            />
          </div>
        </div>
        {screenOrientation.isLandScape || isPhone() || isMachine() ? (
          // <SingleTemplateList
          <MazeSingleTemplateList
            activeGender={activeGender}
            activeTemplate={activeTemplate}
            setActiveTemplate={setActiveTemplate}
            selectTemplateList={selectTemplateList}
            listKey={`${activeGender}-${activeTab}`}
            multiline={false}
          />
        ) : (
          <MultipleTemplateList
            activeGender={activeGender}
            activeTemplate={activeTemplate}
            setActiveTemplate={setActiveTemplate}
            selectTemplateList={selectTemplateList}
            listKey={`${activeGender}-${activeTab}`}
            multiline={screenOrientation.isPortrait}
          />
        )}
        <CreateBtn
          activeTemplate={activeTemplate}
          activeGender={activeGender}
        />
      </div>
      <ThemeDetailModal
        activeGender={activeGender}
        open={themeDetailModalOpen}
        setOpen={setThemeDetailModalOpen}
        themeDetail={activeTemplate}
      />
    </>
  )
}
