import classnames from 'classnames'
import { MyImage } from '@/components/ui/MyImage'
import classNames from 'classnames'
import styles from './MazeSingleTemplate.module.css'
import { useSetAtom, useAtomValue } from 'jotai'
import { isShowThemeDetailModalAtom, screenOrientationAtom } from '@/stores'
import { ThemeDetail } from '@/apis/types'
import { MirrorSexEnum, MirrorAiTaskStatus } from '@/graphqls/types'
import { isIPad, isIphone } from '@/utils'
import { useGlobalVideoPlayer } from '@/hooks/useGlobalVideoPlayer'
import { MyMirrorAiTask } from '@/stores/types'
import MyPlayBtn from '@/components/ui/MyPlayBtn'

/** 单个模版 */
function MazeSingleTemplate({
  item,
  active = false,
  onSelect,
  isMultiple = false,
  className,
  activeGender,
}: {
  item: ThemeDetail
  active: boolean
  isMultiple?: boolean
  className?: string
  activeGender?: string
  onSelect: () => void
}) {
  const setThemeDetailModalOpen = useSetAtom(isShowThemeDetailModalAtom)
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const { openVideoPlayer, openImageViewer } = useGlobalVideoPlayer()

  // 处理视频播放点击事件
  const handleVideoPlay = () => {
    if (item.video && item.video.resultUrl) {
      console.log('🎬 MazeSingleTemplate: 打开视频播放器', {
        themeId: item.id,
        videoUrl: item.video.resultUrl,
        poster:
          activeGender === MirrorSexEnum.FEMALE
            ? item.cover_image_female
            : item.cover_image,
      })

      // 将 ThemeDetail.video 转换为 MyMirrorAiTask 格式
      const videoTask: MyMirrorAiTask = {
        id: `theme-video-${item.id}`,
        status: MirrorAiTaskStatus.SUCCESS,
        resultUrl: item.video.resultUrl,
        detailType: undefined, // 视频类型
        previewUrl: undefined,
        expectedQueueSeconds: undefined,
        generatingCompletePercent: undefined,
        editResultUrls: undefined,
        threeDModelingInfo: undefined,
      }

      // 使用封面图作为海报
      const posterUrl =
        activeGender === MirrorSexEnum.FEMALE
          ? item.cover_image_female
          : item.cover_image

      openVideoPlayer(videoTask, posterUrl)
    }
  }

  // 处理图片查看点击事件
  const handleImageClick = () => {
    const imageUrl =
      activeGender === MirrorSexEnum.FEMALE
        ? item.cover_image_female
        : item.cover_image

    if (imageUrl) {
      console.log('🖼️ MazeSingleTemplate: 打开图片查看器', {
        themeId: item.id,
        imageUrl,
      })
      openImageViewer(imageUrl)
    }
  }

  return (
    <div
      className={classnames(
        className,
        styles.template,
        isMultiple && styles.multiple,
        {
          [styles.active]: active && item.name,
        },
        ' relative'
      )}
      onClick={onSelect}
    >
      {item.name && (
        <div className="maze-theme-title-bg w-full h-[8.75rem] text-[3rem] leading-[8.75rem] font-semibold px-4 rounded-[3rem] ipad:rounded-[2rem] text-white absolute left-0 right-0 bottom-0 text-center text-ellipsis text-nowrap overflow-hidden">
          {item.name}
        </div>
      )}
      {item.female_model_count || item.male_model_count ? (
        <div
          onClick={() => setThemeDetailModalOpen(true)}
          className={classNames(
            'absolute cursor-pointer text-[2rem] w-14 h-14 text-white left-8 bottom-10 rounded-full flex items-center justify-center maze-badge-default',
            screenOrientation.isLandScape ? 'bottom-12' : 'phone:bottom-11'
          )}
        >
          <span>
            {activeGender === MirrorSexEnum.FEMALE
              ? item.female_model_count
              : item.male_model_count}
          </span>
        </div>
      ) : (
        <div className="absolute"></div>
      )}
      {item.video && item.video.resultUrl && (
        <MyPlayBtn type="video" onClick={handleVideoPlay} />
      )}
      {/* 只有生成的图片才显示产看大图功能 */}
      {!item.name && !item.video && (
        <MyPlayBtn type="image" onClick={handleImageClick} />
      )}
      <MyImage
        src={
          activeGender === MirrorSexEnum.FEMALE
            ? item.cover_image_female!
            : item.cover_image!
        }
        tag="v800"
        className={classNames('rounded-[3rem] ipad:rounded-[2rem]', [
          screenOrientation.isPortrait
            ? isIphone()
              ? 'h-[55dvh]'
              : isIPad()
                ? 'h-[62dvh]'
                : 'h-[68dvh]'
            : 'h-[780px]',
        ])}
        imgClassName="object-cover"
      />
    </div>
  )
}

export default MazeSingleTemplate
