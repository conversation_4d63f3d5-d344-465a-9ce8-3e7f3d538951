@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* normal */
  --primary-raw: 191 139 229;
  --primary: #bf8be5;
  --error-raw: 239 70 111;
  --error: #ef466f;
  --success-raw: 69 179 107;
  --success: #45b36b;
  --secondary-raw: 53 57 69;
  --secondary: rgb(53, 57, 69);

  --divider: rgba(0, 0, 0, 0.56);
  --liner-blue-raw: 105 100 222;
  --liner-blue: #6964de;
  --liner-pink-raw: 252 166 233;
  --liner-pink: #fca6e9;

  /* palette */
  --neutral-50-raw: 0 0 0;
  --neutral-50: #000000;
  --neutral-100-raw: 20 20 22;
  --neutral-100: #141416;
  --neutral-200-raw: 35 38 47;
  --neutral-200: #23262f;
  --neutral-300-raw: 53 57 69;
  --neutral-300: #353945;
  --neutral-400-raw: 119 126 144;
  --neutral-400: #777e90;
  --neutral-500-raw: 177 181 196;
  --neutral-500: #b1b5c4;
  --neutral-600-raw: 230 232 236;
  --neutral-600: #e6e8ec;
  --neutral-700-raw: 244 245 246;
  --neutral-700: #f4f5f6;
  --neutral-800-raw: 252 252 253;
  --neutral-800: #fcfcfd;
  --neutral-900-raw: 255 255 255;
  --neutral-900: #ffffff;

  --primary-text: #EDF0F4;
  --secondary-text: #b1b5c4;
  --primary-bg: #23283D;
}

@layer base {
  svg {
    vertical-align: unset;
  }
  img,
  svg,
  video,
  canvas,
  audio,
  iframe,
  embed,
  object {
    display: unset;
  }

  body{
    background: #000;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, .6); /* 替换为你想要的颜色，例如 Tailwind 的 slate-400 */
    border-radius: 9999px;
    border: 2px solid transparent;
    background-clip: content-box;
  }
}

@layer utilities {
  .shadow-button-primary {
    box-shadow: 0px 76px 52px rgba(139, 92, 246, 0.2),
      0px 20px 28px rgba(139, 92, 246, 0.36);
  }
  .shadow-button-neutral {
    box-shadow: 0px 76px 52px rgba(9, 9, 9, 0.06),
      0px 20px 28px rgba(0, 0, 0, 0.12);
  }
  .shadow-box-neutral {
    box-shadow: 0px 48px 64px rgba(0, 0, 0, 0.16);
  }
  .shadow-box-primary {
    box-shadow: 0px 11px 25px rgba(112, 65, 200, 0.1),
      0px 45px 45px rgba(112, 65, 200, 0.09),
      0px 102px 61px rgba(112, 65, 200, 0.05),
      0px 181px 72px rgba(112, 65, 200, 0.01),
      0px 282px 79px rgba(112, 65, 200, 0);
  }
  .shadow-box-small-primary {
    box-shadow: 0px 8px 18px rgba(112, 65, 200, 0.1),
      0px 32px 32px rgba(112, 65, 200, 0.09),
      0px 72px 44px rgba(112, 65, 200, 0.05),
      0px 130px 51px rgba(112, 65, 200, 0.01),
      0px 200px 56px rgba(112, 65, 200, 0);
  }
  .text-gradient-primary {
    background: linear-gradient(321deg, #6964de -10.33%, #fca6e9 100%);
    -webkit-background-clip: text;
    color: transparent;
  }
  .bg-gradient-primary {
    background: linear-gradient(321deg, #6964de -10.33%, #fca6e9 100%);
  }
  .border-primary {
    border-color: #bf8be5;
  }
  .border-light-primary {
    border-color: rgba(139, 92, 246, 0.16);
  }
  .text-primary {
    color: var(--primary);
  }
  /** 主题配置：用于按钮上的文字颜色 */
  .btn-text-color {
    color: var(--neutral-900);
  }
  /** 主题配置：用于背景上的文字颜色 */
  .bg-text-color {
    color: var(--neutral-50);
  }

  .maze-page-title{
    color: var(--primary-text);
    font-size: 3.5rem;
    line-height: 100%;
    text-align: center;
    font-weight: 600;
    @apply ipad:text-[2.8rem];
  }
  .maze-box-shadow{
    box-shadow: 0px 20px 28px 0px rgba(139, 92, 246, 0.36), 0px 76px 52px 0px rgba(139, 92, 246, 0.20);
  }
  .maze-bg-primary{
    background: var(--primary-bg);
  }
  .maze-primary-text{
    color: var(--primary-text);
  }
  .maze-bg-gradient-primary{
    background: linear-gradient(180deg, rgba(255, 119, 119, 0.80) 0%, rgba(240, 221, 255, 0.80) 100%);
  }
  .maze-bg-gradient-disabled{
    background: linear-gradient(180deg, rgba(93, 126, 255, 0.80) 0%, rgba(255, 213, 213, 0.80) 100%);
  }
  .maze-bg-gradient-btn{
    background: linear-gradient(180deg, rgba(144, 77, 221, 0.40) 0%, rgba(171, 201, 227, 0.40) 100%);
  }
  .maze-bg-gradient-card{
    background: linear-gradient(180deg, rgba(42, 58, 122, 0.60) 0%, rgba(195, 129, 202, 0.60) 100%)
  }
  .maze-shoot-btn{
    border: 6px solid #FFF;
    background: rgba(255, 255, 255, 0.20);
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.35);
    backdrop-filter: blur(5px);
  }
  .maze-card-shadow{
    box-shadow: 0px 0px 20px 4px #000 inset, 0px 0px 20px 0px #000 inset;
  }
  .maze-event-card-shadow{
    box-shadow: 0px 0px 40px 10px rgba(104, 106, 255, 0.20), 0px 0px 43.983px 0px #9565FF;
  }
  .maze-event-card-bg{
    background: linear-gradient(180deg, rgba(14, 0, 23, 0.50) 0%, rgba(70, 25, 112, 0.50) 100%);
    box-shadow: 0px 0px 40px 10px rgba(104, 106, 255, 0.20), 0px 0px 43.983px 0px #9565FF;
    backdrop-filter: blur(10px);
  }
  .maze-start-btn{
    background: radial-gradient(circle at 40% 40%, #6a00ff, #36006a);
    box-shadow:
      inset 0 0 30px rgba(255, 255, 255, 0.15),
      0 10px 25px rgba(160, 67, 241, 0.6),
      0 -4px 10px rgba(255, 255, 255, 0.1),
      inset 0 0 10px rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: absolute;
    top: calc(50% - 80px);
    left: calc(50% - 80px);
  }
  .maze-badge-default{
    background: rgba(186, 192, 204, 0.20);
    box-shadow: 0px 0px 4.48px 0px rgba(0, 0, 0, 0.40);
  }
  .maze-loading-card-shadow{
    background: linear-gradient(180deg, #282828 0%, #000 100%);
    box-shadow: 0px 0px 20px 0px rgba(142, 101, 230, 0.40), 0px 0px 15px 0px rgba(104, 245, 255, 0.80), 0px 0px 20px 0px #5100FF;
  }
  .maze-theme-title-bg{
    background: linear-gradient(0deg, #000 0%, rgba(0, 0, 0, 0.00) 100%);
  }
  .mySwiper{
    padding: 16px !important;
  }
  .swiper-overflow-visible .mySwiper{
    overflow: visible !important;
  }
  .no-swiper-overflow-visible .mySwiper{
    overflow: hidden !important;
  }
  .maze-slide-active-shadow{
    box-shadow: 0px 0px 20px 0px rgba(142, 101, 230, 0.40), 0px 0px 15px 0px rgba(104, 245, 255, 0.80), 0px 0px 20px 0px #5100FF;
  }
  /* 自定义Swiper Pagination样式 */
  .swiper-pagination-bullet-custom {
    display: inline-block;
    width: 1.25rem !important;
    height: 1.25rem !important;
    background-color: rgba(156, 163, 175, 0.6) !important; /* 灰色 */
    border-radius: 50% !important;
    opacity: 1 !important;
    margin: 0 6px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
  }

  .swiper-pagination-bullet-active-custom {
    background-color: white !important; /* 白色 */
    transform: scale(1.2) !important;
  }
}
